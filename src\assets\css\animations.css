/*
==============================================
CSS3 ANIMATION CHEAT SHEET
==============================================

Made by <PERSON>

www.justinaguilar.com/animations/

Questions, comments, concerns, love letters:
<EMAIL>
==============================================
*/

/*
==============================================
slideDown
==============================================
*/


.slideDown{
	animation-name: slideDown;
	-webkit-animation-name: slideDown;

	animation-duration: 1s;
	-webkit-animation-duration: 1s;

	animation-timing-function: ease;
	-webkit-animation-timing-function: ease;

	visibility: visible;
}

@keyframes slideDown {
	0% {
		transform: translateY(-100%);
	}
	50%{
		transform: translateY(8%);
	}
	65%{
		transform: translateY(-4%);
	}
	80%{
		transform: translateY(4%);
	}
	95%{
		transform: translateY(-2%);
	}
	100% {
		transform: translateY(0%);
	}
}

@-webkit-keyframes slideDown {
	0% {
		-webkit-transform: translateY(-100%);
	}
	50%{
		-webkit-transform: translateY(8%);
	}
	65%{
		-webkit-transform: translateY(-4%);
	}
	80%{
		-webkit-transform: translateY(4%);
	}
	95%{
		-webkit-transform: translateY(-2%);
	}
	100% {
		-webkit-transform: translateY(0%);
	}
}

/*
==============================================
fadeIn
==============================================
*/

.fadeIn{
	animation-name: fadeIn;
	-webkit-animation-name: fadeIn;

	animation-duration: 1.5s;
	-webkit-animation-duration: 1.5s;

	animation-timing-function: ease-in-out;
	-webkit-animation-timing-function: ease-in-out;

	visibility: visible;
}

@keyframes fadeIn {
	0% {
		transform: scale(0);
		opacity: 0.0;
	}
	60% {
		transform: scale(1.1);
	}
	80% {
		transform: scale(0.9);
		opacity: 1;
	}
	100% {
		transform: scale(1);
		opacity: 1;
	}
}

@-webkit-keyframes fadeIn {
	0% {
		-webkit-transform: scale(0);
		opacity: 0.0;
	}
	60% {
		-webkit-transform: scale(1.1);
	}
	80% {
		-webkit-transform: scale(0.9);
		opacity: 1;
	}
	100% {
		-webkit-transform: scale(1);
		opacity: 1;
	}
}

/*
==============================================
expandOpen
==============================================
*/


.expandOpen{
	animation-name: expandOpen;
	-webkit-animation-name: expandOpen;

	animation-duration: 1.2s;
	-webkit-animation-duration: 1.2s;

	animation-timing-function: ease-out;
	-webkit-animation-timing-function: ease-out;

	visibility: visible;
}

@keyframes expandOpen {
	0% {
		transform: scale(1.8);
	}
	50% {
		transform: scale(0.95);
	}
	80% {
		transform: scale(1.05);
	}
	90% {
		transform: scale(0.98);
	}
	100% {
		transform: scale(1);
	}
}

@-webkit-keyframes expandOpen {
	0% {
		-webkit-transform: scale(1.8);
	}
	50% {
		-webkit-transform: scale(0.95);
	}
	80% {
		-webkit-transform: scale(1.05);
	}
	90% {
		-webkit-transform: scale(0.98);
	}
	100% {
		-webkit-transform: scale(1);
	}
}

/*
==============================================
pulse
==============================================
*/

.pulse{
	animation-name: pulse;
	-webkit-animation-name: pulse;

	animation-duration: 1.5s;
	-webkit-animation-duration: 1.5s;

	animation-iteration-count: infinite;
	-webkit-animation-iteration-count: infinite;
}

@keyframes pulse {
	0% {
		transform: scale(0.9);
		opacity: 0.7;
	}
	50% {
		transform: scale(1);
		opacity: 1;
	}
	100% {
		transform: scale(0.9);
		opacity: 0.7;
	}
}

@-webkit-keyframes pulse {
	0% {
		-webkit-transform: scale(0.95);
		opacity: 0.7;
	}
	50% {
		-webkit-transform: scale(1);
		opacity: 1;
	}
	100% {
		-webkit-transform: scale(0.95);
		opacity: 0.7;
	}
}
