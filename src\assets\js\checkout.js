/*
* Canabro - Medical Marijuana Dispensary HTML Template
* Checkout JavaScript File
*/

(function() {
    "use strict";

    /**
     * Easy selector helper function
     */
    const select = (el, all = false) => {
        el = el.trim();
        if (all) {
            return [...document.querySelectorAll(el)];
        } else {
            return document.querySelector(el);
        }
    };

    /**
     * Easy event listener function
     */
    const on = (type, el, listener, all = false) => {
        let selectEl = select(el, all);
        if (selectEl) {
            if (all) {
                selectEl.forEach(e => e.addEventListener(type, listener));
            } else {
                selectEl.addEventListener(type, listener);
            }
        }
    };

    /**
     * Country and State Dependency
     */
    const countryStateHandler = () => {
        const countrySelect = select('#country');
        const stateSelect = select('#state');
        
        if (countrySelect && stateSelect) {
            countrySelect.addEventListener('change', function() {
                const country = this.value;
                
                // Clear current options
                stateSelect.innerHTML = '<option value="">Select a state</option>';
                
                // Add states based on country
                if (country === 'US') {
                    const usStates = [
                        { code: 'AL', name: 'Alabama' },
                        { code: 'AK', name: 'Alaska' },
                        { code: 'AZ', name: 'Arizona' },
                        { code: 'AR', name: 'Arkansas' },
                        { code: 'CA', name: 'California' },
                        { code: 'CO', name: 'Colorado' },
                        { code: 'CT', name: 'Connecticut' },
                        { code: 'DE', name: 'Delaware' },
                        { code: 'FL', name: 'Florida' },
                        { code: 'GA', name: 'Georgia' },
                        // Add more states as needed
                    ];
                    
                    usStates.forEach(state => {
                        const option = document.createElement('option');
                        option.value = state.code;
                        option.textContent = state.name;
                        stateSelect.appendChild(option);
                    });
                } else if (country === 'CA') {
                    const caProvinces = [
                        { code: 'AB', name: 'Alberta' },
                        { code: 'BC', name: 'British Columbia' },
                        { code: 'MB', name: 'Manitoba' },
                        { code: 'NB', name: 'New Brunswick' },
                        { code: 'NL', name: 'Newfoundland and Labrador' },
                        { code: 'NS', name: 'Nova Scotia' },
                        { code: 'ON', name: 'Ontario' },
                        { code: 'PE', name: 'Prince Edward Island' },
                        { code: 'QC', name: 'Quebec' },
                        { code: 'SK', name: 'Saskatchewan' },
                        // Add more provinces as needed
                    ];
                    
                    caProvinces.forEach(province => {
                        const option = document.createElement('option');
                        option.value = province.code;
                        option.textContent = province.name;
                        stateSelect.appendChild(option);
                    });
                }
                // Add more countries as needed
            });
        }
    };
    
    window.addEventListener('load', countryStateHandler);

    /**
     * Payment Method Toggle
     */
    const paymentMethodToggle = () => {
        const paymentMethods = select('input[name="paymentMethod"]', true);
        const paymentDescriptions = select('.payment-method-description', true);
        
        if (paymentMethods.length > 0) {
            // Hide all descriptions except the first one
            paymentDescriptions.forEach((desc, index) => {
                if (index !== 0) {
                    desc.style.display = 'none';
                }
            });
            
            paymentMethods.forEach((method, index) => {
                method.addEventListener('change', function() {
                    // Hide all descriptions
                    paymentDescriptions.forEach(desc => {
                        desc.style.display = 'none';
                    });
                    
                    // Show the selected method's description
                    if (this.checked && paymentDescriptions[index]) {
                        paymentDescriptions[index].style.display = 'block';
                    }
                });
            });
        }
    };
    
    window.addEventListener('load', paymentMethodToggle);

    /**
     * Form Validation
     */
    const formValidation = () => {
        const checkoutForm = select('#checkoutForm');
        const placeOrderBtn = select('#placeOrderBtn');
        
        if (checkoutForm && placeOrderBtn) {
            placeOrderBtn.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Get all required inputs
                const requiredInputs = checkoutForm.querySelectorAll('[required]');
                let isValid = true;
                
                // Check if all required fields are filled
                requiredInputs.forEach(input => {
                    if (input.value.trim() === '') {
                        isValid = false;
                        input.classList.add('is-invalid');
                    } else {
                        input.classList.remove('is-invalid');
                    }
                });
                
                if (!isValid) {
                    alert('Please fill in all required fields.');
                    return;
                }
                
                // If form is valid, show success message
                alert('Thank you for your order! Your order has been placed successfully.');
                
                // Redirect to order confirmation page
                // window.location.href = 'order-confirmation.html';
            });
        }
    };
    
    window.addEventListener('load', formValidation);

    /**
     * Create Account Toggle
     */
    const createAccountToggle = () => {
        const createAccountCheckbox = select('#createAccount');
        
        if (createAccountCheckbox) {
            createAccountCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    // Show password fields
                    const passwordFields = document.createElement('div');
                    passwordFields.className = 'password-fields';
                    passwordFields.innerHTML = `
                        <div class="form-group">
                            <label for="password">Password <span class="required">*</span></label>
                            <input type="password" class="form-control" id="password" required>
                        </div>
                        <div class="form-group">
                            <label for="confirmPassword">Confirm Password <span class="required">*</span></label>
                            <input type="password" class="form-control" id="confirmPassword" required>
                        </div>
                    `;
                    
                    // Insert after the checkbox
                    this.closest('.form-group').insertAdjacentElement('afterend', passwordFields);
                } else {
                    // Remove password fields
                    const passwordFields = select('.password-fields');
                    if (passwordFields) {
                        passwordFields.remove();
                    }
                }
            });
        }
    };
    
    window.addEventListener('load', createAccountToggle);

})();
