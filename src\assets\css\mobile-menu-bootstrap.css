/* Bootstrap Mobile Menu Custom Styles */
.offcanvas {
    width: 300px !important;
}

.offcanvas-header {
    border-bottom: 2px solid #4caf50;
}

.offcanvas-body {
    padding: 0 !important;
}

.accordion-button {
    background-color: transparent !important;
    border: none !important;
    box-shadow: none !important;
    color: #333 !important;
    font-weight: 500;
}

.accordion-button:not(.collapsed) {
    background-color: #f8f9fa !important;
    color: #4caf50 !important;
}

.accordion-button:focus {
    box-shadow: none !important;
}

.accordion-button::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%234caf50'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e") !important;
}

.accordion-button:not(.collapsed)::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%234caf50'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e") !important;
    transform: rotate(-180deg) !important;
}

.offcanvas-body a {
    transition: all 0.3s ease;
}

.offcanvas-body a:hover {
    background-color: #f8f9fa !important;
    color: #4caf50 !important;
}

.current-page {
    background-color: #4caf50 !important;
    color: white !important;
}

.current-page:hover {
    background-color: #388e3c !important;
    color: white !important;
}

.social-links a {
    color: #4caf50;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: #388e3c;
}

.mobile-nav-toggler {
    background: none !important;
    color: #4caf50 !important;
}

.mobile-nav-toggler:hover {
    color: #388e3c !important;
}

/* Mobile Menu Responsive */
@media (max-width: 991px) {
    .mobile-nav-toggler {
        display: inline-block !important;
    }
}
