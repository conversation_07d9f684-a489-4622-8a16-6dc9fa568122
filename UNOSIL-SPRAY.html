<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UNOSIL SPRAY - Alpinia Biopharma</title>
    <!-- Favicon -->
    <link rel="icon" href="src/assets/images/favicon.png" type="image/png">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Owl Carousel -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.theme.default.min.css">
    <!-- Animate CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="src/assets/css/style-final.css">
    <link rel="stylesheet" href="src/assets/css/menu.css">

    <style>
        .main-slider {
            position: relative;
            height: 100vh;
            overflow: hidden;
        }

        .main-slider .video-container {
            position: relative;
            width: 100%;
            height: 100vh;
        }

        .main-slider .background-video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: 0;
        }

        .main-slider .content {
            position: relative;
            z-index: 2;
            color: #fff;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            padding-top: 40vh;
        }

        .main-slider .video-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.4);
            z-index: 1;
        }

        .main-slider .title {
            font-size: 24px;
            font-weight: 500;
            margin-bottom: 20px;
            color: #fff;
        }

        .main-slider h1 {
            font-size: 60px;
            font-weight: 700;
            margin-bottom: 20px;
            color: #fff;
        }

        .main-slider .text {
            font-size: 18px;
            margin-bottom: 30px;
            max-width: 600px;
        }

        .main-slider .btn-box {
            display: flex;
            gap: 20px;
        }

        .main-slider .theme-btn {
            padding: 15px 30px;
            border-radius: 5px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .main-slider .btn-style-one {
            background: #4caf50;
            color: #fff;
        }

        .main-slider .btn-style-two {
            background: transparent;
            border: 2px solid #fff;
            color: #fff;
        }

        .main-slider .btn-style-one:hover {
            background: #388e3c;
        }

        .main-slider .btn-style-two:hover {
            background: #fff;
            color: #333;
        }

        /* Added subtle box-shadow to feature cards in Features Section */
        .features-section .inner-box {
            box-shadow: 0 4px 16px rgba(0,0,0,0.08);
            border-radius: 10px;
        }

        /* Hide all submenus by default */
        .navigation li ul {
            display: none;
            position: absolute;
            left: 0;
            top: 100%;
            min-width: 220px;
            background: #fff;
            box-shadow: 0 8px 24px rgba(0,0,0,0.08);
            z-index: 100;
            border-radius: 0 0 8px 8px;
            padding: 0;
        }

        /* Show submenu on hover */
        .navigation li.dropdown:hover > ul,
        .navigation li.dropdown:focus-within > ul {
            display: block;
        }

        /* For nested submenus (third level) */
        .navigation li ul li.dropdown > ul {
            left: 100%;
            top: 0;
            margin-left: 1px;
            border-radius: 0 8px 8px 8px;
        }

        /* Style submenu links */
        .navigation li ul li a {
            color: #333;
            padding: 12px 20px;
            display: block;
            font-size: 15px;
            background: #fff;
            border-bottom: 1px solid #f0f0f0;
            transition: background 0.2s;
            white-space: nowrap;
        }

        .navigation li ul li a:hover {
            background: #f8f9fa;
            color: #4caf50;
        }

        /* Add arrow indicators for dropdowns */
        .navigation li.dropdown > a:after {
            content: '\f107';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            margin-left: 5px;
            font-size: 12px;
        }

        .navigation li ul li.dropdown > a:after {
            content: '\f105';
            float: right;
        }

        /* Professional styles for Product page */
        .page-title {
            background: white;
            color: #333;
            padding: 4rem 0;
            margin-bottom: 4rem;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .page-title::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('src/assets/images/pattern.png') repeat;
            opacity: 0.05;
        }

        .page-title h1 {
            font-size: 2.8rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            text-align: center;
        }

        .page-title p {
            font-size: 1.1rem;
            opacity: 0.9;
            max-width: 800px;
            margin: 0 auto;
            position: relative;
            text-align: center;
            line-height: 1.6;
        }

        .product-section {
            background: white;
            border-radius: 8px;
            padding: 1.5rem 1rem;
            margin-bottom: 1.2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }

        .product-section:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transform: translateY(-3px);
        }

        .product-title {
            color: #1a4b2c;
            font-size: 1.4rem;
            margin-bottom: 0.6rem;
            font-weight: 600;
            position: relative;
            padding-bottom: 0.8rem;
        }

        .product-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: #2d7d46;
            border-radius: 2px;
        }

        .product-subtitle {
            color: #2d7d46;
            font-size: 1.1rem;
            margin-bottom: 0.7rem;
            font-weight: 500;
        }

        .product-section p {
            color: #4a4a4a;
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .features-list {
            list-style: none;
            margin: 1rem 0;
        }

        .features-list li {
            margin-bottom: 0.6rem;
            padding: 0.7rem 1rem;
            background: #f8f9fa;
            border-radius: 6px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .features-list li:hover {
            background: #f0f7f3;
            border-color: #2d7d46;
            transform: translateX(5px);
        }

        .features-list li:hover .emoji {
            color: #2d7d46;
        }

        .emoji {
            font-size: 1.3rem;
            margin-right: 1rem;
            transition: all 0.3s ease;
            color: #1a4b2c;
        }

        .uses-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 0.7rem;
            margin: 1rem 0;
        }

        .uses-list li {
            background: #f8f9fa;
            padding: 0.8rem;
            border-radius: 6px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.05);
            position: relative;
        }

        .uses-list li:hover {
            background: #f0f7f3;
            border-color: #2d7d46;
            transform: translateY(-5px);
        }

        .uses-list li .emoji {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            display: block;
        }

        .uses-list li span {
            color: #4a4a4a;
            font-weight: 500;
        }

        /* Section Headers */
        h4 {
            color: #1a4b2c;
            font-size: 1.1rem;
            font-weight: 600;
            margin: 1rem 0 0.7rem;
            position: relative;
            padding-left: 1rem;
        }

        h4::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 20px;
            background: #2d7d46;
            border-radius: 2px;
        }

        .product-image {
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .product-image:hover {
            transform: scale(1.02);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        /* Product Gallery Styles */
        .product-gallery {
            position: relative;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .gallery-main {
            position: relative;
            height: 500px;
            overflow: hidden;
            background: #f8f9fa;
        }

        .gallery-item {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 0.5s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .gallery-item.active {
            opacity: 1;
        }

        .gallery-item img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            border-radius: 8px;
        }

        .gallery-item video {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            border-radius: 8px;
        }

        .gallery-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255,255,255,0.9);
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .gallery-nav:hover {
            background: #fff;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .gallery-nav.prev {
            left: 20px;
        }

        .gallery-nav.next {
            right: 20px;
        }

        .gallery-thumbnails {
            display: flex;
            gap: 10px;
            padding: 20px;
            background: #fff;
            overflow-x: auto;
        }

        .thumbnail {
            flex-shrink: 0;
            width: 80px;
            height: 80px;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            border: 3px solid transparent;
            transition: all 0.3s ease;
            position: relative;
        }

        .thumbnail.active {
            border-color: #4caf50;
        }

        .thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .thumbnail.video::after {
            content: '\f04b';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #fff;
            font-size: 20px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.5);
        }

        .thumbnail.video {
            background: linear-gradient(45deg, #4caf50, #2d7d46);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .gallery-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.7);
            color: #fff;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 14px;
            z-index: 10;
        }

        /* Zoom functionality */
        .zoom-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.9);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
        }

        .zoom-content {
            max-width: 90%;
            max-height: 90%;
            position: relative;
        }

        .zoom-close {
            position: absolute;
            top: -40px;
            right: 0;
            background: none;
            border: none;
            color: #fff;
            font-size: 30px;
            cursor: pointer;
        }

        @media (max-width: 768px) {
            .gallery-main {
                height: 350px;
            }

            .gallery-nav {
                width: 40px;
                height: 40px;
            }

            .thumbnail {
                width: 60px;
                height: 60px;
            }
        }
    </style>
</head>
<body>
   <!-- Top Bar -->
      <div class="top-bar">
        <div class="container">
            <div class="row">
                <div class="col-lg-6 col-md-6">
                    <div class="top-bar-left">
                        <ul class="contact-info">
                            <li><i class="fas fa-phone"></i> +91 (*************</li>
                            <li><i class="fas fa-envelope"></i> <EMAIL></li>
                            <li><i class="fas fa-map-marker-alt"></i> Alpinia Headquarters, India</li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6">
                    <div class="top-bar-right">
                        <div class="social-links">
                            <a href="#"><i class="fab fa-facebook-f"></i></a>
                            <a href="#"><i class="fab fa-twitter"></i></a>
                            <a href="#"><i class="fab fa-instagram"></i></a>
                            <a href="#"><i class="fab fa-linkedin-in"></i></a>
                        </div>
                        <!-- Removed user-links (Login/Register) -->
                        <!-- <div class="user-links">
                            <a href="login.html"><i class="fas fa-user"></i> Login</a>
                            <a href="register.html"><i class="fas fa-user-plus"></i> Register</a>
                        </div> -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Header -->
    <header id="header" class="header">
        <!-- Top Header Section -->
        <div class="header-top">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-3 col-md-4 col-sm-4 col-6">
                        <div class="logo">
                            <a href="index.html">
                                <img src="src/assets/images/logo.png" alt="Alpinia Biopharma Logo">
                            </a>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-4 col-sm-4 col-6">
                        <div class="header-search">
                            <form class="search-form">
                                <select class="category-select">
                                    <option value="">Select category</option>
                                    <option value="">WOUND & BURN CARE</option>
                                    <option value="">ENT RANGE</option>
                                    <option value="">GYNAECOLOGY RANGE</option>
                                    <option value="">ANIMAL PRODUCT RANGE</option>
                                </select>
                                <input type="text" placeholder="Search products..." class="search-input">
                                <button type="submit" class="search-btn"><i class="fas fa-search"></i></button>
                            </form>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-4 col-sm-4 d-none d-sm-block">
                        <div class="header-right">
                            <!-- Removed login button -->
                            <!-- <div class="login-btn">
                                <a href="login.html" class="btn-login">LOGIN</a>
                            </div> -->
                           
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Navigation Bar -->
        <div class="main-nav-bar">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-10 col-md-8 col-sm-8 col-6">
                        <div class="nav-outer">
                            <div class="mobile-nav-toggler"><span class="icon flaticon-menu"><i class="fas fa-bars"></i></span></div>
                            <nav class="main-menu navbar-expand-md navbar-light">
                                <div class="collapse navbar-collapse" id="navbarSupportedContent">

                                    <ul class="navigation">
                                        <li class="current"><a href="index.html">Home</a></li>
                                        <li><a href="about.html">About us</a></li>
                                        <li class="dropdown">
                                            <a href="products.html">Products</a>
                                            <ul>
                                                <li class="dropdown">
                                                    <a href="WOUND-BURN-CARE.html">WOUND & BURN CARE</a>
                                                    <ul>
                                                        <li><a href="UNOSIL550-GEL.html">UNOSIL550 GEL</a></li>
                                                        <li><a href="UNOSIL-SPRAY.html">UNOSIL SPRAY</a></li>
                                                    </ul>
                                                </li>
                                                <li class="dropdown">
                                                    <a href="#">ENT RANGE</a>
                                                    <ul>
                                                        <li><a href="SILVORAL-MOUTH-GARGLE.html">SILVORAL MOUTH GARGLE</a></li>
                                                        <li><a href="SILVORAL-MOUTH-SPRAY.html">SILVORAL MOUTH SPRAY</a></li>
                                                        <li><a href="UNOSIL-N-NASAL-SPRAY.html">UNOSIL N NASAL SPRAY</a></li>
                                                        <li><a href="SILVOTIK-EAR-DROPS.html">SILVOTIK EAR DROPS</a></li>
                                                    </ul>
                                                </li>
                                                <li class="dropdown">
                                                    <a href="#">GYNAECOLOGY RANGE</a>
                                                    <ul>
                                                        <li><a href="UNOSIL-V-GEL.html">UNOSIL V GEL</a></li>
                                                    </ul>
                                                </li>
                                                <li class="dropdown">
                                                    <a href="#">ANIMAL PRODUCT RANGE</a>
                                                    <ul>
                                                        <li><a href="UNOVET-ANIMAL-WOUND-SPRAY.html">UNOVET ANIMAL WOUND SPRAY</a></li>
                                                    </ul>
                                                </li>
                                            </ul>
                                        </li>
                                        <li><a href="blog.html">Blog</a></li>
                                        <li><a href="contact.html">Contact</a></li>
                                    </ul>
                                </div>
                            </nav>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-4 col-6">
                        <div class="language-selector">
                            <select class="form-select">
                                <option value="en">English</option>
                                <option value="hi">Hindi</option>
                                <option value="es">Spanish</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

        <!-- Main Header Section -->
        

    <!-- Page Title Section -->
    <div class="page-title">
        <div class="container">
            <h1>UNOSIL SPRAY</h1>
            <p>Antimicrobial Wound Cleaning & Healing Spray</p>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <div class="row">
            <!-- Product Gallery -->
            <div class="col-lg-6 mb-4">
                <div class="product-gallery">
                    <!-- Gallery Indicator -->
                    <div class="gallery-indicator">
                        <span id="current-item">1</span> / <span id="total-items">2</span>
                    </div>

                    <!-- Main Gallery Display -->
                    <div class="gallery-main">
                        <!-- Navigation Buttons -->
                        <button class="gallery-nav prev" onclick="changeGalleryItem(-1)">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="gallery-nav next" onclick="changeGalleryItem(1)">
                            <i class="fas fa-chevron-right"></i>
                        </button>

                        <!-- Gallery Items -->
                        <div class="gallery-item active" onclick="openZoom(this)">
                            <img src="src/assets/images/products/Unosil Spray.jpg" alt="UNOSIL SPRAY - Product Image">
                        </div>
                        <div class="gallery-item">
                            <video controls poster="src/assets/images/products/Unosil Spray.jpg">
                                <source src="src/assets/images/products/unosil_spray.mp4" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                        </div>
                    </div>

                    <!-- Thumbnails -->
                    <div class="gallery-thumbnails">
                        <div class="thumbnail active" onclick="showGalleryItem(0)">
                            <img src="src/assets/images/products/Unosil Spray.jpg" alt="Product Image">
                        </div>
                        <div class="thumbnail video" onclick="showGalleryItem(1)">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Product Details -->
            <div class="col-lg-6">
                <div class="product-section">
                    <h2 class="product-title">UNOSIL SPRAY</h2>
                    <h3 class="product-subtitle">Antimicrobial Wound Cleaning & Healing Spray</h3>
                    <p>Unosil Spray is a convenient, non-touch antimicrobial spray designed for wound cleansing, debridement, and accelerated healing. Powered by Ionic Silver Technology, it offers a safe, effective, and easy-to-use solution that creates a protective barrier, reduces infection risk, and promotes a moist healing environment—all without harming healthy tissue.</p>

                    <h4>Why Choose Unosil Spray?</h4>
                    <ul class="features-list">
                        <li><span class="emoji">⚪</span> Cleans, Protects & Heals - Gently debrides and disinfects the wound area while promoting tissue regeneration</li>
                        <li><span class="emoji">🛡</span> Forms an Antimicrobial Barrier – Shields the wound from further infections & microbial contamination</li>
                        <li><span class="emoji">💧</span> Maintains Moist Wound Environment – Supports optimal healing by preventing dryness and irritation</li>
                        <li><span class="emoji">🧫</span> Prevents Biofilm Formation - Disrupts microbial colonies that often delay healing in chronic wounds</li>
                        <li><span class="emoji">🧬</span> Biocompatible & Non-Toxic - Safe for cells and surrounding tissues—ideal for regular use</li>
                        <li><span class="emoji">⏱</span> Extended Action - Long-lasting presence on the wound site for 48–72 hours, reducing dressing changes</li>
                    </ul>

                    <h4>🩺 Recommended For:</h4>
                    <ul class="uses-list">
                        <li><span class="emoji"></span> <span>Wound debridement & cleansing</span></li>
                        <li><span class="emoji"></span> <span>Post-surgical incisions</span></li>
                        <li><span class="emoji"></span> <span>Diabetic wounds</span></li>
                        <li><span class="emoji"></span> <span>Pressure ulcers & bed sores</span></li>
                        <li><span class="emoji"></span> <span>Minor cuts, abrasions, and chronic injuries</span></li>
                        <li><span class="emoji"></span> <span>Wounds prone to infection or requiring non-touch application</span></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer Section -->
       <!-- Footer -->
    <footer class="main-footer">
        <div class="widgets-section">
            <div class="container">
                <div class="row">
                    <!-- Footer Column 1 -->
                    <div class="col-lg-3 col-md-6 footer-column">
                        <div class="footer-widget about-widget">
                            <div class="logo">
                                <a href="index.html">
                                    <img src="src/assets/images/logo.png" alt="Alpinia Biopharma Logo">
                                </a>
                            </div>
                            <div class="text">Alpinia Biopharma Reema Pvt. Ltd. is a leading provider of advanced healthcare solutions powered by Ionic Silver Technology, bringing innovative antimicrobial products to healthcare professionals and patients worldwide.</div>
                            <div class="social-links">
                                <a href="#"><i class="fab fa-facebook-f"></i></a>
                                <a href="#"><i class="fab fa-twitter"></i></a>
                                <a href="#"><i class="fab fa-instagram"></i></a>
                                <a href="#"><i class="fab fa-linkedin-in"></i></a>
                            </div>
                        </div>
                    </div>

                    <!-- Footer Column 2 -->
                    <div class="col-lg-3 col-md-6 footer-column">
                        <div class="footer-widget links-widget">
                            <h3 class="widget-title">Quick Links</h3>
                            <ul class="user-links">
                                <li><a href="index.html">Home</a></li>
                                <li><a href="about.html">About Us</a></li>
                                <li><a href="products.html">Products</a></li>
                                <li><a href="blog.html">Blog</a></li>
                                <li><a href="contact.html">Contact Us</a></li>
                            </ul>
                        </div>
                    </div>

                    <!-- Footer Column 3 -->
                    <div class="col-lg-3 col-md-6 footer-column">
                        <div class="footer-widget links-widget">
                            <h3 class="widget-title">Categories</h3>
                            <ul class="user-links">
                                <li><a href="products.html">WOUND & BURN CARE</a></li>
                                <li><a href="products.html">ENT RANGE</a></li>
                                <li><a href="products.html">GYNAECOLOGY RANGE</a></li>
                                <li><a href="products.html">ANIMAL PRODUCT RANGE</a></li>
                                
                            </ul>
                        </div>
                    </div>

                    <!-- Footer Column 4 -->
                    <div class="col-lg-3 col-md-6 footer-column">
                        <div class="footer-widget contact-widget">
                            <h3 class="widget-title">Contact</h3>
                            <div class="widget-content">
                                <ul class="contact-info">
                                    <li>
                                        <i class="fas fa-map-marker-alt"></i>
                                        <span>Alpinia Headquarters, Mumbai, India</span>
                                    </li>
                                    <li>
                                        <i class="fas fa-phone"></i>
                                        <span>+91 (*************</span>
                                    </li>
                                    <li>
                                        <i class="fas fa-envelope"></i>
                                        <span><EMAIL></span>
                                    </li>
                                    <li>
                                        <i class="fas fa-clock"></i>
                                        <span>Mon - Fri: 9am - 8pm<br>Sat - Sun: 10am - 6pm</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <div class="container">
                <div class="inner-container">
                    <div class="copyright-text">
                        <p>&copy; 2025 <a href="index.html">Alpinia Biopharma</a>. All Rights Reserved.</p>
                    </div>
                    <div class="payment-methods">
                        <img src="https://cdn.pixabay.com/photo/2021/12/06/13/48/payment-6850733_1280.png" alt="Payment Methods" style="height: 30px;">
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Zoom Overlay -->
    <div class="zoom-overlay" id="zoomOverlay" onclick="closeZoom()">
        <div class="zoom-content" onclick="event.stopPropagation()">
            <button class="zoom-close" onclick="closeZoom()">
                <i class="fas fa-times"></i>
            </button>
            <img id="zoomImage" src="" alt="Zoomed Product Image">
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"></script>
    <script src="src/assets/js/main.js"></script>

    <!-- Product Gallery Script -->
    <script>
        let currentGalleryIndex = 0;
        const galleryItems = document.querySelectorAll('.gallery-item');
        const thumbnails = document.querySelectorAll('.thumbnail');
        const totalItems = galleryItems.length;

        // Update total items display
        document.getElementById('total-items').textContent = totalItems;

        function showGalleryItem(index) {
            // Hide all gallery items
            galleryItems.forEach(item => item.classList.remove('active'));
            thumbnails.forEach(thumb => thumb.classList.remove('active'));

            // Show selected item
            galleryItems[index].classList.add('active');
            thumbnails[index].classList.add('active');

            currentGalleryIndex = index;
            document.getElementById('current-item').textContent = index + 1;

            // Pause all videos when switching
            const videos = document.querySelectorAll('.gallery-item video');
            videos.forEach(video => {
                video.pause();
                video.currentTime = 0;
            });
        }

        function changeGalleryItem(direction) {
            let newIndex = currentGalleryIndex + direction;

            if (newIndex >= totalItems) {
                newIndex = 0;
            } else if (newIndex < 0) {
                newIndex = totalItems - 1;
            }

            showGalleryItem(newIndex);
        }

        function openZoom(element) {
            const img = element.querySelector('img');
            if (img) {
                document.getElementById('zoomImage').src = img.src;
                document.getElementById('zoomOverlay').style.display = 'flex';
                document.body.style.overflow = 'hidden';
            }
        }

        function closeZoom() {
            document.getElementById('zoomOverlay').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (document.getElementById('zoomOverlay').style.display === 'flex') {
                if (e.key === 'Escape') {
                    closeZoom();
                }
            } else {
                if (e.key === 'ArrowLeft') {
                    changeGalleryItem(-1);
                } else if (e.key === 'ArrowRight') {
                    changeGalleryItem(1);
                }
            }
        });

        // Touch/swipe support for mobile
        let touchStartX = 0;
        let touchEndX = 0;

        document.querySelector('.gallery-main').addEventListener('touchstart', function(e) {
            touchStartX = e.changedTouches[0].screenX;
        });

        document.querySelector('.gallery-main').addEventListener('touchend', function(e) {
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        });

        function handleSwipe() {
            const swipeThreshold = 50;
            const diff = touchStartX - touchEndX;

            if (Math.abs(diff) > swipeThreshold) {
                if (diff > 0) {
                    changeGalleryItem(1); // Swipe left - next image
                } else {
                    changeGalleryItem(-1); // Swipe right - previous image
                }
            }
        }
    </script>
</body>
</html> 