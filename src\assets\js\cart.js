/*
* Canabro - Medical Marijuana Dispensary HTML Template
* Shopping Cart JavaScript File
*/

(function() {
    "use strict";

    /**
     * Easy selector helper function
     */
    const select = (el, all = false) => {
        el = el.trim();
        if (all) {
            return [...document.querySelectorAll(el)];
        } else {
            return document.querySelector(el);
        }
    };

    /**
     * Easy event listener function
     */
    const on = (type, el, listener, all = false) => {
        let selectEl = select(el, all);
        if (selectEl) {
            if (all) {
                selectEl.forEach(e => e.addEventListener(type, listener));
            } else {
                selectEl.addEventListener(type, listener);
            }
        }
    };

    /**
     * Quantity Input
     */
    const quantityInputs = () => {
        const minusBtns = select('.quantity-btn.minus', true);
        const plusBtns = select('.quantity-btn.plus', true);
        const quantityInputs = select('.product-quantity input', true);
        
        if (minusBtns.length > 0 && plusBtns.length > 0 && quantityInputs.length > 0) {
            minusBtns.forEach((btn, index) => {
                btn.addEventListener('click', () => {
                    let currentValue = parseInt(quantityInputs[index].value);
                    if (currentValue > 1) {
                        quantityInputs[index].value = currentValue - 1;
                        updateCartItemTotal(index);
                        updateCartTotal();
                    }
                });
            });
            
            plusBtns.forEach((btn, index) => {
                btn.addEventListener('click', () => {
                    let currentValue = parseInt(quantityInputs[index].value);
                    if (currentValue < 10) {
                        quantityInputs[index].value = currentValue + 1;
                        updateCartItemTotal(index);
                        updateCartTotal();
                    }
                });
            });
            
            quantityInputs.forEach((input, index) => {
                input.addEventListener('change', () => {
                    let currentValue = parseInt(input.value);
                    if (currentValue < 1) {
                        input.value = 1;
                    } else if (currentValue > 10) {
                        input.value = 10;
                    }
                    updateCartItemTotal(index);
                    updateCartTotal();
                });
            });
        }
    };
    
    window.addEventListener('load', quantityInputs);

    /**
     * Update Cart Item Total
     */
    const updateCartItemTotal = (index) => {
        const cartItems = select('.cart-item', true);
        
        if (cartItems.length > 0 && index < cartItems.length) {
            const item = cartItems[index];
            const price = parseFloat(item.querySelector('.product-price').textContent.replace('$', ''));
            const quantity = parseInt(item.querySelector('.product-quantity input').value);
            const totalElement = item.querySelector('.product-total');
            
            const total = price * quantity;
            totalElement.textContent = `$${total.toFixed(2)}`;
        }
    };

    /**
     * Update Cart Total
     */
    const updateCartTotal = () => {
        const cartItems = select('.cart-item', true);
        const subtotalElement = select('.summary-item:nth-child(1) .summary-value');
        const shippingElement = select('.summary-item:nth-child(2) .summary-value');
        const taxElement = select('.summary-item:nth-child(3) .summary-value');
        const totalElement = select('.summary-item.total .summary-value');
        
        if (cartItems.length > 0 && subtotalElement && shippingElement && taxElement && totalElement) {
            let subtotal = 0;
            
            cartItems.forEach(item => {
                const itemTotal = parseFloat(item.querySelector('.product-total').textContent.replace('$', ''));
                subtotal += itemTotal;
            });
            
            const shipping = parseFloat(shippingElement.textContent.replace('$', ''));
            const tax = subtotal * 0.1; // Assuming 10% tax
            const total = subtotal + shipping + tax;
            
            subtotalElement.textContent = `$${subtotal.toFixed(2)}`;
            taxElement.textContent = `$${tax.toFixed(2)}`;
            totalElement.textContent = `$${total.toFixed(2)}`;
        }
    };

    /**
     * Remove Cart Item
     */
    const removeCartItem = () => {
        const removeBtns = select('.remove-btn', true);
        
        if (removeBtns.length > 0) {
            removeBtns.forEach((btn, index) => {
                btn.addEventListener('click', function() {
                    const cartItem = this.closest('.cart-item');
                    
                    if (cartItem) {
                        cartItem.remove();
                        updateCartTotal();
                        
                        // Check if cart is empty
                        const remainingItems = select('.cart-item', true);
                        if (remainingItems.length === 0) {
                            const cartTable = select('.cart-table');
                            const cartActions = select('.cart-actions');
                            
                            if (cartTable && cartActions) {
                                cartTable.innerHTML = '<div class="empty-cart"><p>Your cart is empty.</p><a href="products.html" class="btn btn-primary">Shop Now</a></div>';
                                cartActions.style.display = 'none';
                            }
                        }
                    }
                });
            });
        }
    };
    
    window.addEventListener('load', removeCartItem);

    /**
     * Update Cart Button
     */
    const updateCartButton = () => {
        const updateCartBtn = select('.update-cart');
        
        if (updateCartBtn) {
            updateCartBtn.addEventListener('click', function() {
                updateCartTotal();
                alert('Cart updated successfully!');
            });
        }
    };
    
    window.addEventListener('load', updateCartButton);

    /**
     * Apply Coupon
     */
    const applyCoupon = () => {
        const couponForm = select('.coupon');
        
        if (couponForm) {
            const couponInput = couponForm.querySelector('input');
            const couponBtn = couponForm.querySelector('button');
            
            couponBtn.addEventListener('click', function() {
                const couponCode = couponInput.value.trim();
                
                if (couponCode === '') {
                    alert('Please enter a coupon code.');
                    return;
                }
                
                // Here you would normally validate the coupon code with the server
                // For this example, we'll just show an alert
                alert('Coupon applied successfully!');
                
                // Apply a discount (for example purposes)
                const subtotalElement = select('.summary-item:nth-child(1) .summary-value');
                const subtotal = parseFloat(subtotalElement.textContent.replace('$', ''));
                const discount = subtotal * 0.1; // 10% discount
                
                // Add discount line to summary
                const summaryItems = select('.summary-item', true);
                const totalItem = select('.summary-item.total');
                
                // Check if discount line already exists
                const existingDiscount = select('.summary-item.discount');
                
                if (!existingDiscount) {
                    const discountItem = document.createElement('div');
                    discountItem.className = 'summary-item discount';
                    discountItem.innerHTML = `
                        <span class="summary-label">Discount (10%)</span>
                        <span class="summary-value">-$${discount.toFixed(2)}</span>
                    `;
                    
                    totalItem.parentNode.insertBefore(discountItem, totalItem);
                }
                
                updateCartTotal();
            });
        }
    };
    
    window.addEventListener('load', applyCoupon);

})();
