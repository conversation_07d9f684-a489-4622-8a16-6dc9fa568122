/* Menu Styles */
.menu {
  position: relative;
  display: inline-block;
}

.menu-content {
  display: none;
  position: absolute;
  background-color: #f9f9f9;
  min-width: 200px;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 1;
  border-radius: 4px;
  padding: 8px 0;
}

.menu-content a {
  color: black;
  padding: 12px 16px;
  text-decoration: none;
  display: block;
  transition: background-color 0.3s;
}

.menu-content a:hover {
  background-color: #f1f1f1;
}

.menu:hover .menu-content {
  display: block;
}

.menu:hover .dropbtn {
  background-color: #3e8e41;
}

/* Submenu Styles */
.submenu {
  position: relative;
  display: inline-block;
}

.submenu-content {
  display: none;
  position: absolute;
  left: 100%;
  top: 0;
  background-color: #f9f9f9;
  min-width: 200px;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 1;
  border-radius: 4px;
  padding: 8px 0;
}

.submenu:hover .submenu-content {
  display: block;
}

.submenu-content a {
  color: black;
  padding: 12px 16px;
  text-decoration: none;
  display: block;
  transition: background-color 0.3s;
}

.submenu-content a:hover {
  background-color: #f1f1f1;
}

/* Menu Item Styles */
.menu-item {
  position: relative;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.menu-item:hover {
  background-color: #f1f1f1;
}

.menu-item::after {
  content: "▼";
  font-size: 12px;
  margin-left: 8px;
}

/* Submenu Item Styles */
.submenu-item {
  position: relative;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.submenu-item:hover {
  background-color: #f1f1f1;
}

.submenu-item::after {
  content: "►";
  font-size: 12px;
  margin-left: 8px;
  position: absolute;
  right: 16px;
}

.main-slider {
  position: relative;
  height: 100vh;
  overflow: hidden;
}

.main-slider .video-container {
  position: relative;
  width: 100%;
  height: 100vh;
}

.main-slider .background-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 0;
}

.main-slider .content {
  position: relative;
  z-index: 2;
  color: #fff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  padding-top: 40vh;
}

.main-slider .video-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1;
}

.main-slider .title {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 20px;
  color: #fff;
}

.main-slider h1 {
  font-size: 60px;
  font-weight: 700;
  margin-bottom: 20px;
  color: #fff;
}

.main-slider .text {
  font-size: 18px;
  margin-bottom: 30px;
  max-width: 600px;
}

.main-slider .btn-box {
  display: flex;
  gap: 20px;
}

.main-slider .theme-btn {
  padding: 15px 30px;
  border-radius: 5px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.main-slider .btn-style-one {
  background: #4caf50;
  color: #fff;
}

.main-slider .btn-style-two {
  background: transparent;
  border: 2px solid #fff;
  color: #fff;
}

.main-slider .btn-style-one:hover {
  background: #388e3c;
}

.main-slider .btn-style-two:hover {
  background: #fff;
  color: #333;
}

/* Added subtle box-shadow to feature cards in Features Section */
.features-section .inner-box {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border-radius: 10px;
}

/* Hide all submenus by default */
.navigation li ul {
  display: none;
  position: absolute;
  left: 0;
  top: 100%;
  min-width: 220px;
  background: #fff;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
  z-index: 100;
  border-radius: 0 0 8px 8px;
  padding: 0;
}

/* Show submenu on hover */
.navigation li.dropdown:hover > ul,
.navigation li.dropdown:focus-within > ul {
  display: block;
}

/* For nested submenus (third level) */
.navigation li ul li.dropdown > ul {
  left: 100%;
  top: 0;
  margin-left: 1px;
  border-radius: 0 8px 8px 8px;
}

/* Style submenu links */
.navigation li ul li a {
  color: #333;
  padding: 12px 20px;
  display: block;
  font-size: 15px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  transition: background 0.2s;
  white-space: nowrap;
}

.navigation li ul li a:hover {
  background: #f8f9fa;
  color: #4caf50;
}

/* Add arrow indicators for dropdowns */
.navigation li.dropdown > a:after {
  content: "\f107";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  margin-left: 5px;
  font-size: 12px;
}

.navigation li ul li.dropdown > a:after {
  content: "\f105";
  float: right;
}
