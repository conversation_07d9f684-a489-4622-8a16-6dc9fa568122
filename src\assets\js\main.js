/*
* Canabro - Medical Marijuana Dispensary HTML Template
* Main JavaScript File
*/

(function() {
    "use strict";

    /**
     * Easy selector helper function
     */
    const select = (el, all = false) => {
        el = el.trim();
        if (all) {
            return [...document.querySelectorAll(el)];
        } else {
            return document.querySelector(el);
        }
    };

    /**
     * Easy event listener function
     */
    const on = (type, el, listener, all = false) => {
        let selectEl = select(el, all);
        if (selectEl) {
            if (all) {
                selectEl.forEach(e => e.addEventListener(type, listener));
            } else {
                selectEl.addEventListener(type, listener);
            }
        }
    };

    /**
     * Easy on scroll event listener
     */
    const onscroll = (el, listener) => {
        el.addEventListener('scroll', listener);
    };

    /**
     * Navbar links active state on scroll
     */
    let navbarlinks = select('#navbarNav .nav-link', true);
    const navbarlinksActive = () => {
        let position = window.scrollY + 200;
        navbarlinks.forEach(navbarlink => {
            if (!navbarlink.hash) return;
            let section = select(navbarlink.hash);
            if (!section) return;
            if (position >= section.offsetTop && position <= (section.offsetTop + section.offsetHeight)) {
                navbarlink.classList.add('active');
            } else {
                navbarlink.classList.remove('active');
            }
        });
    };
    window.addEventListener('load', navbarlinksActive);
    onscroll(document, navbarlinksActive);

    /**
     * Scrolls to an element with header offset
     */
    const scrollto = (el) => {
        let header = select('#header');
        let offset = header.offsetHeight;

        let elementPos = select(el).offsetTop;
        window.scrollTo({
            top: elementPos - offset,
            behavior: 'smooth'
        });
    };

    /**
     * Toggle .header-scrolled class to #header when page is scrolled
     */
    let selectHeader = select('#header');
    if (selectHeader) {
        const headerScrolled = () => {
            if (window.scrollY > 100) {
                selectHeader.classList.add('header-scrolled');
            } else {
                selectHeader.classList.remove('header-scrolled');
            }
        };
        window.addEventListener('load', headerScrolled);
        onscroll(document, headerScrolled);
    }

    /**
     * Back to top button
     */
    let backtotop = select('.back-to-top');
    if (backtotop) {
        const toggleBacktotop = () => {
            if (window.scrollY > 100) {
                backtotop.classList.add('active');
            } else {
                backtotop.classList.remove('active');
            }
        };
        window.addEventListener('load', toggleBacktotop);
        onscroll(document, toggleBacktotop);
    }

    /**
     * Mobile nav toggle
     */
    on('click', '.mobile-nav-toggle', function(e) {
        select('#navbar').classList.toggle('navbar-mobile');
        this.classList.toggle('bi-list');
        this.classList.toggle('bi-x');
    });

    /**
     * Mobile nav dropdowns activate
     */
    on('click', '.navbar .dropdown > a', function(e) {
        if (select('#navbar').classList.contains('navbar-mobile')) {
            e.preventDefault();
            this.nextElementSibling.classList.toggle('dropdown-active');
        }
    }, true);

    /**
     * Scroll with offset on links with a class name .scrollto
     */
    on('click', '.scrollto', function(e) {
        if (select(this.hash)) {
            e.preventDefault();

            let navbar = select('#navbar');
            if (navbar.classList.contains('navbar-mobile')) {
                navbar.classList.remove('navbar-mobile');
                let navbarToggle = select('.mobile-nav-toggle');
                navbarToggle.classList.toggle('bi-list');
                navbarToggle.classList.toggle('bi-x');
            }
            scrollto(this.hash);
        }
    }, true);

    /**
     * Scroll with offset on page load with hash links in the url
     */
    window.addEventListener('load', () => {
        if (window.location.hash) {
            if (select(window.location.hash)) {
                scrollto(window.location.hash);
            }
        }
    });

    /**
     * Animation on scroll
     */
    window.addEventListener('load', () => {
        if (typeof AOS !== 'undefined') {
            AOS.init({
                duration: 1000,
                easing: 'ease-in-out',
                once: true,
                mirror: false
            });
        }
    });

    /**
     * Product Filter
     */
    const productFilter = () => {
        let filterButtons = select('.product-filter li', true);
        let productItems = select('.product-item', true);

        if (filterButtons.length > 0) {
            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons
                    filterButtons.forEach(btn => btn.classList.remove('active'));

                    // Add active class to clicked button
                    this.classList.add('active');

                    // Get filter value
                    let filterValue = this.getAttribute('data-filter');

                    // Filter products
                    productItems.forEach(item => {
                        if (filterValue === '*' || item.classList.contains(filterValue.substring(1))) {
                            item.style.display = 'block';
                        } else {
                            item.style.display = 'none';
                        }
                    });
                });
            });
        }
    };

    window.addEventListener('load', productFilter);

    /**
     * Testimonial Slider
     */
    const testimonialSlider = () => {
        let testimonialItems = select('.testimonial-item', true);
        let currentIndex = 0;

        if (testimonialItems.length > 0) {
            // Hide all testimonials except the first one
            testimonialItems.forEach((item, index) => {
                if (index !== 0) {
                    item.style.display = 'none';
                }
            });

            // Auto slide function
            const autoSlide = () => {
                testimonialItems[currentIndex].style.display = 'none';
                currentIndex = (currentIndex + 1) % testimonialItems.length;
                testimonialItems[currentIndex].style.display = 'block';
            };

            // Set interval for auto slide
            setInterval(autoSlide, 5000);
        }
    };

    window.addEventListener('load', testimonialSlider);

    /**
     * Contact Form Validation
     */
    const contactForm = select('#contactForm');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Simple validation
            let name = select('#name').value;
            let email = select('#email').value;
            let subject = select('#subject').value;
            let message = select('#message').value;

            if (name === '' || email === '' || subject === '' || message === '') {
                alert('Please fill in all fields');
                return;
            }

            // If validation passes, you would normally send the form data to a server
            alert('Thank you for your message. We will get back to you soon!');
            this.reset();
        });
    }

    /**
     * Age Verification Popup
     */
    const ageVerification = () => {
        const ageVerificationPopup = select('#ageVerification');
        const ageYesBtn = select('#ageYes');
        const ageNoBtn = select('#ageNo');

        // Check if user has already verified age
        if (localStorage.getItem('ageVerified') === 'true') {
            ageVerificationPopup.style.display = 'none';
        }

        // Yes button click
        if (ageYesBtn) {
            ageYesBtn.addEventListener('click', () => {
                localStorage.setItem('ageVerified', 'true');
                ageVerificationPopup.style.display = 'none';

                // Show newsletter popup after 5 seconds
                setTimeout(() => {
                    showNewsletterPopup();
                }, 5000);
            });
        }

        // No button click
        if (ageNoBtn) {
            ageNoBtn.addEventListener('click', () => {
                window.location.href = 'https://www.google.com';
            });
        }
    };

    window.addEventListener('load', ageVerification);

    /**
     * Newsletter Popup
     */
    const showNewsletterPopup = () => {
        const newsletterPopup = select('#newsletterPopup');
        const closeNewsletterBtn = select('#closeNewsletter');
        const newsletterForm = select('#newsletterForm');

        // Check if user has already closed the newsletter popup
        if (localStorage.getItem('newsletterClosed') !== 'true') {
            newsletterPopup.style.display = 'flex';
        }

        // Close button click
        if (closeNewsletterBtn) {
            closeNewsletterBtn.addEventListener('click', () => {
                newsletterPopup.style.display = 'none';
                localStorage.setItem('newsletterClosed', 'true');
            });
        }

        // Form submission
        if (newsletterForm) {
            newsletterForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Simple validation
                let email = this.querySelector('input[type="email"]').value;

                if (email === '') {
                    alert('Please enter your email address');
                    return;
                }

                // If validation passes, you would normally send the form data to a server
                alert('Thank you for subscribing to our newsletter!');
                this.reset();
                newsletterPopup.style.display = 'none';
                localStorage.setItem('newsletterSubscribed', 'true');
            });
        }
    };

    // Show newsletter popup after 30 seconds if age is already verified
    if (localStorage.getItem('ageVerified') === 'true' && localStorage.getItem('newsletterSubscribed') !== 'true') {
        setTimeout(() => {
            showNewsletterPopup();
        }, 30000);
    }

})();
