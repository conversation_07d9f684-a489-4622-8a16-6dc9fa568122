/*
* Canabro - Medical Marijuana Dispensary HTML Template
* Product Details JavaScript File
*/

(function() {
    "use strict";

    /**
     * Easy selector helper function
     */
    const select = (el, all = false) => {
        el = el.trim();
        if (all) {
            return [...document.querySelectorAll(el)];
        } else {
            return document.querySelector(el);
        }
    };

    /**
     * Easy event listener function
     */
    const on = (type, el, listener, all = false) => {
        let selectEl = select(el, all);
        if (selectEl) {
            if (all) {
                selectEl.forEach(e => e.addEventListener(type, listener));
            } else {
                selectEl.addEventListener(type, listener);
            }
        }
    };

    /**
     * Product Gallery
     */
    const productGallery = () => {
        const mainImage = select('#mainImage');
        const thumbnails = select('.thumbnail', true);
        
        if (thumbnails.length > 0 && mainImage) {
            thumbnails.forEach(thumbnail => {
                thumbnail.addEventListener('click', function() {
                    // Remove active class from all thumbnails
                    thumbnails.forEach(thumb => thumb.classList.remove('active'));
                    
                    // Add active class to clicked thumbnail
                    this.classList.add('active');
                    
                    // Update main image
                    const imageUrl = this.getAttribute('data-image');
                    mainImage.src = imageUrl;
                });
            });
        }
    };
    
    window.addEventListener('load', productGallery);

    /**
     * Quantity Input
     */
    const quantityInput = () => {
        const minusBtn = select('.quantity-btn.minus');
        const plusBtn = select('.quantity-btn.plus');
        const quantityInput = select('#quantity');
        
        if (minusBtn && plusBtn && quantityInput) {
            minusBtn.addEventListener('click', () => {
                let currentValue = parseInt(quantityInput.value);
                if (currentValue > 1) {
                    quantityInput.value = currentValue - 1;
                    updateTotalPrice();
                }
            });
            
            plusBtn.addEventListener('click', () => {
                let currentValue = parseInt(quantityInput.value);
                if (currentValue < 10) {
                    quantityInput.value = currentValue + 1;
                    updateTotalPrice();
                }
            });
            
            quantityInput.addEventListener('change', () => {
                let currentValue = parseInt(quantityInput.value);
                if (currentValue < 1) {
                    quantityInput.value = 1;
                } else if (currentValue > 10) {
                    quantityInput.value = 10;
                }
                updateTotalPrice();
            });
        }
    };
    
    window.addEventListener('load', quantityInput);

    /**
     * Weight Options
     */
    const weightOptions = () => {
        const weightOptionBtns = select('.weight-option', true);
        const priceElement = select('.product-price .price');
        const weightElement = select('.product-price .weight');
        
        if (weightOptionBtns.length > 0 && priceElement && weightElement) {
            weightOptionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // Remove active class from all weight options
                    weightOptionBtns.forEach(option => option.classList.remove('active'));
                    
                    // Add active class to clicked option
                    this.classList.add('active');
                    
                    // Update price and weight
                    const price = this.getAttribute('data-price');
                    const weight = this.getAttribute('data-weight');
                    
                    priceElement.textContent = `$${price}`;
                    weightElement.textContent = `/ ${weight}`;
                    
                    updateTotalPrice();
                });
            });
        }
    };
    
    window.addEventListener('load', weightOptions);

    /**
     * Update Total Price
     */
    const updateTotalPrice = () => {
        const priceElement = select('.product-price .price');
        const quantityInput = select('#quantity');
        
        if (priceElement && quantityInput) {
            const price = parseFloat(priceElement.textContent.replace('$', ''));
            const quantity = parseInt(quantityInput.value);
            
            // You can display the total price somewhere if needed
            // For example: select('.total-price').textContent = `$${(price * quantity).toFixed(2)}`;
        }
    };

    /**
     * Add to Cart
     */
    const addToCart = () => {
        const addToCartBtn = select('.add-to-cart');
        
        if (addToCartBtn) {
            addToCartBtn.addEventListener('click', function() {
                const productName = select('.product-details h2').textContent;
                const price = select('.product-price .price').textContent;
                const quantity = select('#quantity').value;
                const weight = select('.weight-option.active').getAttribute('data-weight');
                
                // Here you would normally add the product to the cart
                // For this example, we'll just show an alert
                alert(`Added to cart: ${quantity} x ${productName} (${weight}) - ${price} each`);
            });
        }
    };
    
    window.addEventListener('load', addToCart);

    /**
     * Wishlist
     */
    const wishlist = () => {
        const wishlistBtn = select('.wishlist');
        
        if (wishlistBtn) {
            wishlistBtn.addEventListener('click', function() {
                const icon = this.querySelector('i');
                
                if (icon.classList.contains('far')) {
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                    alert('Added to wishlist!');
                } else {
                    icon.classList.remove('fas');
                    icon.classList.add('far');
                    alert('Removed from wishlist!');
                }
            });
        }
    };
    
    window.addEventListener('load', wishlist);

})();
